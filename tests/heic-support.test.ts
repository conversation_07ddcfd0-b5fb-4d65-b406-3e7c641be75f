import { describe, it, expect, beforeEach, vi } from 'vitest';
import { detectFileType, FileType, processDocument } from '../src/utils/ocr';
import { processHeicFile } from '../src/utils/heic-converter';
import fs from 'fs';

// Mock fs module
vi.mock('fs');

const mockFs = vi.mocked(fs);

describe('HEIC File Support', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('detectFileType', () => {
    it('should detect HEIC files by signature', async () => {
      const heicSignature = Buffer.from([
        0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63
      ]);

      // Mock file existence and reading
      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockReturnValue(3 as any);
      mockFs.readSync.mockImplementation((fd, buffer, offset, length, position) => {
        if (buffer instanceof Buffer && length >= 12) {
          heicSignature.copy(buffer, 0, 0, 12);
        }
        return 12;
      });
      mockFs.closeSync.mockImplementation(() => {});

      const result = await detectFileType('/test/image.heic');

      expect(result).toBe(FileType.IMAGE);
      expect(mockFs.existsSync).toHaveBeenCalledWith('/test/image.heic');
    });

    it('should detect HEIF files by signature', async () => {
      const heifSignature = Buffer.from([
        0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x6D, 0x69, 0x66, 0x31
      ]);

      // Mock file existence and reading
      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockReturnValue(3 as any);
      mockFs.readSync.mockImplementation((fd, buffer, offset, length, position) => {
        if (buffer instanceof Buffer && length >= 12) {
          heifSignature.copy(buffer, 0, 0, 12);
        }
        return 12;
      });
      mockFs.closeSync.mockImplementation(() => {});

      const result = await detectFileType('/test/image.heif');

      expect(result).toBe(FileType.IMAGE);
      expect(mockFs.existsSync).toHaveBeenCalledWith('/test/image.heif');
    });

    it('should detect HEIC files by extension when signature fails', async () => {
      const unknownSignature = Buffer.from([0x00, 0x00, 0x00, 0x00]);

      // Mock file existence and reading
      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockReturnValue(3 as any);
      mockFs.readSync.mockImplementation((fd, buffer, offset, length, position) => {
        if (buffer instanceof Buffer) {
          unknownSignature.copy(buffer, 0, 0, Math.min(buffer.length, unknownSignature.length));
        }
        return unknownSignature.length;
      });
      mockFs.closeSync.mockImplementation(() => {});

      const result = await detectFileType('/test/image.heic');

      expect(result).toBe(FileType.IMAGE);
    });

    it('should detect HEIF files by extension when signature fails', async () => {
      const unknownSignature = Buffer.from([0x00, 0x00, 0x00, 0x00]);

      // Mock file existence and reading
      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockReturnValue(3 as any);
      mockFs.readSync.mockImplementation((fd, buffer, offset, length, position) => {
        if (buffer instanceof Buffer) {
          unknownSignature.copy(buffer, 0, 0, Math.min(buffer.length, unknownSignature.length));
        }
        return unknownSignature.length;
      });
      mockFs.closeSync.mockImplementation(() => {});

      const result = await detectFileType('/test/image.heif');

      expect(result).toBe(FileType.IMAGE);
    });

    it('should handle file read errors gracefully', async () => {
      // Mock file existence but reading error
      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockImplementation(() => {
        throw new Error('File read error');
      });

      const result = await detectFileType('/test/image.heic');

      expect(result).toBe(FileType.UNSUPPORTED);
    });

    it('should handle non-existent files', async () => {
      mockFs.existsSync.mockReturnValue(false);

      const result = await detectFileType('/test/nonexistent.heic');

      expect(result).toBe(FileType.UNSUPPORTED);
    });
  });

  describe('File Extension Support', () => {
    const testCases = [
      { extension: '.heic', expected: FileType.IMAGE },
      { extension: '.heif', expected: FileType.IMAGE },
      { extension: '.HEIC', expected: FileType.IMAGE },
      { extension: '.HEIF', expected: FileType.IMAGE },
      { extension: '.jpg', expected: FileType.IMAGE },
      { extension: '.jpeg', expected: FileType.IMAGE },
      { extension: '.png', expected: FileType.IMAGE },
      { extension: '.pdf', expected: FileType.PDF },
      { extension: '.txt', expected: FileType.UNSUPPORTED }
    ];

    testCases.forEach(({ extension, expected }) => {
      it(`should detect ${extension} files correctly by extension`, async () => {
        const unknownSignature = Buffer.from([0x00, 0x00, 0x00, 0x00]);

        // Mock file existence and reading with unknown signature
        mockFs.existsSync.mockReturnValue(true);
        mockFs.openSync.mockReturnValue(3 as any);
        mockFs.readSync.mockImplementation((fd, buffer, offset, length, position) => {
          if (buffer instanceof Buffer) {
            unknownSignature.copy(buffer, 0, 0, Math.min(buffer.length, unknownSignature.length));
          }
          return unknownSignature.length;
        });
        mockFs.closeSync.mockImplementation(() => {});

        const result = await detectFileType(`/test/file${extension}`);

        expect(result).toBe(expected);
      });
    });
  });

  describe('HEIC Signature Validation', () => {
    it('should correctly identify HEIC signature bytes', () => {
      const heicSignature = [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63];

      // This test validates that our signature detection logic is correct
      // HEIC files start with a specific byte sequence
      expect(heicSignature.slice(4, 8)).toEqual([0x66, 0x74, 0x79, 0x70]); // 'ftyp'
      expect(heicSignature.slice(8, 12)).toEqual([0x68, 0x65, 0x69, 0x63]); // 'heic'
    });

    it('should correctly identify HEIF signature bytes', () => {
      const heifSignature = [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x6D, 0x69, 0x66, 0x31];

      // This test validates that our signature detection logic is correct
      // HEIF files start with a specific byte sequence
      expect(heifSignature.slice(4, 8)).toEqual([0x66, 0x74, 0x79, 0x70]); // 'ftyp'
      expect(heifSignature.slice(8, 12)).toEqual([0x6D, 0x69, 0x66, 0x31]); // 'mif1'
    });
  });

  describe('HEIC Conversion Integration', () => {
    it('should handle HEIC conversion in OCR pipeline', async () => {
      // This test verifies that HEIC files are properly handled in the OCR pipeline
      // In a real environment, this would test the full conversion workflow

      const heicSignature = Buffer.from([
        0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63
      ]);

      mockFs.existsSync.mockReturnValue(true);
      mockFs.openSync.mockReturnValue(3 as any);
      mockFs.readSync.mockImplementation((_fd: any, buffer: any, _offset: any, length: any, _position: any) => {
        if (buffer instanceof Buffer && length >= 12) {
          heicSignature.copy(buffer, 0, 0, 12);
        }
        return 12;
      });
      mockFs.closeSync.mockImplementation(() => {});

      const result = await processHeicFile('/test/image.heic');

      // Should detect as HEIC file
      expect(result.originalPath).toBe('/test/image.heic');

      // In test environment, conversion will fail but detection should work
      // In real environment with heic-convert, this would succeed
      expect(result.success).toBe(false); // Expected in test environment
    });
  });
});
