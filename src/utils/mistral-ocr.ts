import {
  MistralOcrRequest,
  MistralOcrResponse,
  MistralOcrError,
  MistralOcrProcessResult,
  MistralOcrConfig,
  TempFileInfo
} from '../types/mistral-ocr';
import { uploadTempFile } from './temp-storage';

// Default configuration
const DEFAULT_CONFIG: Partial<MistralOcrConfig> = {
  baseUrl: 'https://api.mistral.ai/v1/ocr',
  timeout: 60000, // 60 seconds
  includeImages: true
};

/**
 * Get Mistral OCR configuration from environment
 */
const getMistralConfig = (): MistralOcrConfig => {
  const apiKey = process.env.MISTRAL_API_KEY;
  
  if (!apiKey) {
    throw new Error('MISTRAL_API_KEY environment variable is required');
  }

  return {
    apiKey,
    ...DEFAULT_CONFIG
  } as MistralOcrConfig;
};

/**
 * Make a request to Mistral OCR API
 */
const callMistralOcrApi = async (
  request: MistralOcrRequest,
  config: MistralOcrConfig
): Promise<MistralOcrResponse> => {
  try {
    console.log('Making request to Mistral OCR API...');
    console.log('Request:', JSON.stringify(request, null, 2));

    const response = await fetch(config.baseUrl!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify(request),
      signal: AbortSignal.timeout(config.timeout!)
    });

    const responseData = await response.json();

    if (!response.ok) {
      const errorData = responseData as MistralOcrError;
      console.error('Mistral OCR API error:', errorData);
      throw new Error(`Mistral OCR API error: ${errorData.error.message}`);
    }

    console.log('Mistral OCR API response received successfully');
    return responseData as MistralOcrResponse;

  } catch (error) {
    console.error('Error calling Mistral OCR API:', error);
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new Error('Mistral OCR API request timed out');
      }
      throw error;
    }
    throw new Error('Unknown error calling Mistral OCR API');
  }
};

/**
 * Extract text from response
 */
const extractTextFromResponse = (response: MistralOcrResponse): string => {
  try {
    if (!response.choices || response.choices.length === 0) {
      throw new Error('No choices in Mistral OCR response');
    }

    const choice = response.choices[0];
    if (!choice.message || !choice.message.content) {
      throw new Error('No content in Mistral OCR response');
    }

    return choice.message.content.trim();
  } catch (error) {
    console.error('Error extracting text from Mistral OCR response:', error);
    throw error;
  }
};

/**
 * Process an image file with Mistral OCR
 */
export const processImageWithMistral = async (
  filePath: string,
  originalFileName?: string
): Promise<MistralOcrProcessResult> => {
  let tempFile: TempFileInfo | null = null;

  try {
    console.log('=== PROCESSING IMAGE WITH MISTRAL OCR ===');
    console.log('File path:', filePath);

    // Get configuration
    const config = getMistralConfig();

    // Upload file to temporary storage
    tempFile = await uploadTempFile(filePath, originalFileName);

    // Create request
    const request: MistralOcrRequest = {
      model: 'mistral-ocr-latest',
      document: {
        type: 'image_url',
        image_url: tempFile.url
      },
      include_image_base64: config.includeImages
    };

    // Call Mistral OCR API
    const response = await callMistralOcrApi(request, config);

    // Extract text
    const text = extractTextFromResponse(response);

    console.log('Mistral OCR processing completed successfully');
    console.log('Extracted text length:', text.length);

    return {
      success: true,
      text,
      images: response.images
    };

  } catch (error) {
    console.error('Error processing image with Mistral OCR:', error);
    return {
      success: false,
      text: '',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  } finally {
    // Cleanup temp file
    if (tempFile) {
      try {
        await tempFile.cleanup();
      } catch (cleanupError) {
        console.warn('Error cleaning up temp file:', cleanupError);
      }
    }
  }
};

/**
 * Process a PDF file with Mistral OCR
 */
export const processPdfWithMistral = async (
  filePath: string,
  originalFileName?: string
): Promise<MistralOcrProcessResult> => {
  let tempFile: TempFileInfo | null = null;

  try {
    console.log('=== PROCESSING PDF WITH MISTRAL OCR ===');
    console.log('File path:', filePath);

    // Get configuration
    const config = getMistralConfig();

    // Upload file to temporary storage
    tempFile = await uploadTempFile(filePath, originalFileName);

    // Create request
    const request: MistralOcrRequest = {
      model: 'mistral-ocr-latest',
      document: {
        type: 'document_url',
        document_url: tempFile.url
      },
      include_image_base64: config.includeImages
    };

    // Call Mistral OCR API
    const response = await callMistralOcrApi(request, config);

    // Extract text
    const text = extractTextFromResponse(response);

    console.log('Mistral OCR PDF processing completed successfully');
    console.log('Extracted text length:', text.length);

    return {
      success: true,
      text,
      images: response.images
    };

  } catch (error) {
    console.error('Error processing PDF with Mistral OCR:', error);
    return {
      success: false,
      text: '',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  } finally {
    // Cleanup temp file
    if (tempFile) {
      try {
        await tempFile.cleanup();
      } catch (cleanupError) {
        console.warn('Error cleaning up temp file:', cleanupError);
      }
    }
  }
};

/**
 * Universal document processing with Mistral OCR
 */
export const processDocumentWithMistral = async (
  filePath: string,
  fileType: 'image' | 'pdf',
  originalFileName?: string
): Promise<MistralOcrProcessResult> => {
  try {
    console.log('=== PROCESSING DOCUMENT WITH MISTRAL OCR ===');
    console.log('File path:', filePath);
    console.log('File type:', fileType);

    if (fileType === 'pdf') {
      return await processPdfWithMistral(filePath, originalFileName);
    } else {
      return await processImageWithMistral(filePath, originalFileName);
    }

  } catch (error) {
    console.error('Error processing document with Mistral OCR:', error);
    return {
      success: false,
      text: '',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
